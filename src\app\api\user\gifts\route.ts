import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's gifts with related data
    const { data: gifts, error: giftsError } = await supabase
      .from('user_gifts')
      .select(`
        id,
        points_used,
        received_at,
        notes,
        order_id,
        gift_threshold_id,
        products_received,
        gift_thresholds (
          id,
          threshold_points,
          is_active
        )
      `)
      .eq('user_id', user.id)
      .order('received_at', { ascending: false })

    if (giftsError) {
      console.error('Error fetching user gifts:', giftsError)
      return NextResponse.json({ error: 'Error fetching gifts' }, { status: 500 })
    }

    // Get product details for all products in the gifts
    const allProductIds = gifts?.flatMap(gift => gift.products_received || []) || []
    const uniqueProductIds = [...new Set(allProductIds)]

    let products: any[] = []
    if (uniqueProductIds.length > 0) {
      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select('id, title, images, price, discount_price, category')
        .in('id', uniqueProductIds)

      if (productsError) {
        console.error('Error fetching products:', productsError)
      } else {
        products = productsData || []
      }
    }

    // Enrich gifts with product details
    const enrichedGifts = gifts?.map(gift => ({
      ...gift,
      products: (gift.products_received || []).map(productId => 
        products.find(p => p.id === productId)
      ).filter(Boolean)
    })) || []

    return NextResponse.json({ 
      success: true, 
      gifts: enrichedGifts 
    })

  } catch (error) {
    console.error('Error in gifts API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
