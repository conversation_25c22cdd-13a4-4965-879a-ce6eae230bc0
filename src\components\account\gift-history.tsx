'use client';

import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Gift, Calendar, Star, Package } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import Image from 'next/image';

interface Product {
  id: string;
  title: string;
  images: string[];
  price: number;
  discount_price?: number;
  category: string;
}

interface GiftThreshold {
  id: string;
  threshold_points: number;
  is_active: boolean;
}

interface UserGift {
  id: string;
  points_used: number;
  received_at: string;
  notes?: string;
  order_id?: string;
  gift_threshold_id: string;
  products_received: string[];
  gift_thresholds: GiftThreshold;
  products: Product[];
}

export default function GiftHistory() {
  const [gifts, setGifts] = useState<UserGift[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const t = useTranslations('account');

  useEffect(() => {
    fetchGifts();
  }, []);

  const fetchGifts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/user/gifts');
      
      if (!response.ok) {
        throw new Error('Failed to fetch gifts');
      }
      
      const data = await response.json();
      setGifts(data.gifts || []);
    } catch (err) {
      console.error('Error fetching gifts:', err);
      setError('Failed to load gift history');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('it-CH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            {t('gifts.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            {t('gifts.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (gifts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            {t('gifts.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Gift className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{t('gifts.noGifts')}</h3>
            <p className="text-muted-foreground">
              {t('gifts.noGiftsDescription')}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Gift className="h-5 w-5" />
          {t('gifts.title')}
        </CardTitle>
        <p className="text-muted-foreground">
          {t('gifts.description')}
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {gifts.map((gift) => (
            <Card key={gift.id} className="border-l-4 border-l-green-500">
              <CardContent className="pt-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="bg-green-100 p-2 rounded-full">
                      <Star className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold">
                        {t('gifts.giftReceived')}
                      </h4>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        {formatDate(gift.received_at)}
                      </div>
                    </div>
                  </div>
                  <Badge variant="secondary" className="bg-green-50 text-green-700">
                    {gift.points_used} {t('gifts.points')}
                  </Badge>
                </div>

                {gift.products && gift.products.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm font-medium">
                      <Package className="h-4 w-4" />
                      {t('gifts.productsReceived')}:
                    </div>
                    <div className="grid gap-3 md:grid-cols-2">
                      {gift.products.map((product) => (
                        <div key={product.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          {product.images && product.images[0] && (
                            <Image
                              src={product.images[0]}
                              alt={product.title}
                              width={48}
                              height={48}
                              className="rounded object-cover"
                            />
                          )}
                          <div className="flex-1 min-w-0">
                            <p className="font-medium text-sm truncate">
                              {product.title}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {product.category}
                            </p>
                            <p className="text-xs font-medium text-green-600">
                              {t('gifts.freeGift')}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {gift.notes && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">{gift.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
